import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  TextInput,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { auth, db } from "../config/firebase";
import {
  collection,
  getDocs,
  doc,
  updateDoc,
  query,
  where,
} from "@react-native-firebase/firestore";
import { red } from "./colors";

const DevTools = () => {
  const insets = useSafeAreaInsets();
  const [courts, setCourts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [updating, setUpdating] = useState(false);

  // Court characteristics state
  const [boardSize, setBoardSize] = useState(null);
  const [lines, setLines] = useState(null);
  const [lighting, setLighting] = useState(null);
  const [fullCourt, setFullCourt] = useState(null);
  const [indoorOutdoor, setIndoorOutdoor] = useState(null);
  const [publicPrivate, setPublicPrivate] = useState(null);
  const [waterFountain, setWaterFountain] = useState(null);
  const [seating, setSeating] = useState(null);
  const [fencing, setFencing] = useState(null);
  const [accessible, setAccessible] = useState(null);

  // Fetch all courts from courts-cz1 collection
  const fetchCourts = async () => {
    try {
      setLoading(true);
      const courtsRef = collection(db, "courts-cz1");
      const querySnapshot = await getDocs(courtsRef);
      
      const courtsData = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.address && data.address !== "") {
          courtsData.push({
            id: doc.id,
            ...data,
          });
        }
      });
      
      setCourts(courtsData);
    } catch (error) {
      console.error("Error fetching courts:", error);
      Alert.alert("Error", "Failed to fetch courts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourts();
  }, []);

  // Reset form when selecting a court
  const selectCourt = (court) => {
    setSelectedCourt(court);
    
    // Set existing values or null if not present
    setBoardSize(court.boardSize === "regular" ? 1 : court.boardSize === "small" ? 0 : null);
    setLines(court.lines === true ? 1 : court.lines === false ? 0 : null);
    setLighting(court.lighting === true ? 1 : court.lighting === false ? 0 : null);
    setFullCourt(court.fullCourt === true ? 1 : court.fullCourt === false ? 0 : null);
    setIndoorOutdoor(court.indoorOutdoor === "outdoor" ? 1 : court.indoorOutdoor === "indoor" ? 0 : null);
    setPublicPrivate(court.publicPrivate === "public" ? 1 : court.publicPrivate === "private" ? 0 : null);
    setWaterFountain(court.waterFountain === true ? 1 : court.waterFountain === false ? 0 : null);
    setSeating(court.seating === true ? 1 : court.seating === false ? 0 : null);
    setFencing(court.fencing === true ? 1 : court.fencing === false ? 0 : null);
    setAccessible(court.accessible === true ? 1 : court.accessible === false ? 0 : null);
  };

  // Update court with new characteristics
  const updateCourt = async () => {
    if (!selectedCourt) return;

    try {
      setUpdating(true);
      
      const courtRef = doc(db, "courts-cz1", selectedCourt.id);
      const updateData = {};

      // Only update fields that have been set
      if (boardSize !== null) {
        updateData.boardSize = boardSize === 1 ? "regular" : "small";
      }
      if (lines !== null) {
        updateData.lines = lines === 1;
      }
      if (lighting !== null) {
        updateData.lighting = lighting === 1;
      }
      if (fullCourt !== null) {
        updateData.fullCourt = fullCourt === 1;
      }
      if (indoorOutdoor !== null) {
        updateData.indoorOutdoor = indoorOutdoor === 1 ? "outdoor" : "indoor";
      }
      if (publicPrivate !== null) {
        updateData.publicPrivate = publicPrivate === 1 ? "public" : "private";
      }
      if (waterFountain !== null) {
        updateData.waterFountain = waterFountain === 1;
      }
      if (seating !== null) {
        updateData.seating = seating === 1;
      }
      if (fencing !== null) {
        updateData.fencing = fencing === 1;
      }
      if (accessible !== null) {
        updateData.accessible = accessible === 1;
      }

      await updateDoc(courtRef, updateData);
      
      Alert.alert("Success", "Court updated successfully!");
      setSelectedCourt(null);
      fetchCourts(); // Refresh the list
      
    } catch (error) {
      console.error("Error updating court:", error);
      Alert.alert("Error", "Failed to update court");
    } finally {
      setUpdating(false);
    }
  };

  const renderCourtItem = ({ item }) => (
    <TouchableOpacity
      style={styles.courtItem}
      onPress={() => selectCourt(item)}
    >
      <Text style={styles.courtName}>{item.address}</Text>
      <Text style={styles.courtDetails}>
        Baskets: {item.baskets || "N/A"} | Surface: {item.surface || "N/A"}
      </Text>
      <Text style={styles.courtId}>ID: {item.id}</Text>
    </TouchableOpacity>
  );

  const OptionButton = ({ title, value, onPress, option1, option2 }) => (
    <View style={styles.optionContainer}>
      <Text style={styles.optionTitle}>{title}</Text>
      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[
            styles.optionButton,
            value === 1 && styles.selectedButton,
          ]}
          onPress={() => onPress(1)}
        >
          <Text style={[styles.buttonText, value === 1 && styles.selectedText]}>
            {option1}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.optionButton,
            value === 0 && styles.selectedButton,
          ]}
          onPress={() => onPress(0)}
        >
          <Text style={[styles.buttonText, value === 0 && styles.selectedText]}>
            {option2}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={red} />
        <Text>Loading courts...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Text style={styles.header}>Dev Tools - Update Courts</Text>
      
      {!selectedCourt ? (
        <FlatList
          data={courts}
          renderItem={renderCourtItem}
          keyExtractor={(item) => item.id}
          style={styles.courtsList}
        />
      ) : (
        <ScrollView style={styles.formContainer}>
          <Text style={styles.selectedCourtTitle}>
            Updating: {selectedCourt.address}
          </Text>
          
          <OptionButton
            title="Board Size"
            value={boardSize}
            onPress={setBoardSize}
            option1="Regular"
            option2="Small"
          />
          
          <OptionButton
            title="Lines"
            value={lines}
            onPress={setLines}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Lighting"
            value={lighting}
            onPress={setLighting}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Full Court"
            value={fullCourt}
            onPress={setFullCourt}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Indoor/Outdoor"
            value={indoorOutdoor}
            onPress={setIndoorOutdoor}
            option1="Outdoor"
            option2="Indoor"
          />
          
          <OptionButton
            title="Public/Private"
            value={publicPrivate}
            onPress={setPublicPrivate}
            option1="Public"
            option2="Private"
          />
          
          <OptionButton
            title="Water Fountain"
            value={waterFountain}
            onPress={setWaterFountain}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Seating"
            value={seating}
            onPress={setSeating}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Fencing"
            value={fencing}
            onPress={setFencing}
            option1="Yes"
            option2="No"
          />
          
          <OptionButton
            title="Accessible"
            value={accessible}
            onPress={setAccessible}
            option1="Yes"
            option2="No"
          />
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setSelectedCourt(null)}
            >
              <Text style={styles.backButtonText}>Back to List</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.updateButton, updating && styles.disabledButton]}
              onPress={updateCourt}
              disabled={updating}
            >
              {updating ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.updateButtonText}>Update Court</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
    padding: 20,
  },
  header: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
    color: red,
  },
  courtsList: {
    flex: 1,
  },
  courtItem: {
    backgroundColor: "#f5f5f5",
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  courtName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  courtDetails: {
    fontSize: 14,
    color: "#666",
    marginBottom: 3,
  },
  courtId: {
    fontSize: 12,
    color: "#999",
  },
  formContainer: {
    flex: 1,
  },
  selectedCourtTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  optionContainer: {
    marginBottom: 20,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  optionButton: {
    flex: 1,
    padding: 12,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    marginHorizontal: 5,
    alignItems: "center",
  },
  selectedButton: {
    backgroundColor: red,
  },
  buttonText: {
    fontSize: 14,
    color: "#333",
  },
  selectedText: {
    color: "white",
    fontWeight: "bold",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 30,
    marginBottom: 20,
  },
  backButton: {
    flex: 1,
    padding: 15,
    backgroundColor: "#ccc",
    borderRadius: 8,
    marginRight: 10,
    alignItems: "center",
  },
  backButtonText: {
    fontSize: 16,
    color: "#333",
  },
  updateButton: {
    flex: 1,
    padding: 15,
    backgroundColor: red,
    borderRadius: 8,
    marginLeft: 10,
    alignItems: "center",
  },
  updateButtonText: {
    fontSize: 16,
    color: "white",
    fontWeight: "bold",
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
});

export default DevTools;

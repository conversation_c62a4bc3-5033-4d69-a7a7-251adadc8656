import React, { useState, useEffect, useCallback } from "react";
import {
  Alert,
  Platform,
  ActivityIndicator,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  AppState,
} from "react-native";
import { NavigationContainer, useNavigation } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { onAuthStateChanged } from "@react-native-firebase/auth";

import { doc, getDoc } from "@react-native-firebase/firestore";

import * as SplashScreen from "expo-splash-screen";
import { useFonts } from "expo-font";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Provider, useDispatch, useSelector } from "react-redux";
import store from "./store";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  AntDesign,
  MaterialCommunityIcons,
  Feather,
  Ionicons,
} from "@expo/vector-icons";
import {
  setJSEx<PERSON><PERSON><PERSON><PERSON>,
  setNativeExceptionHandler,
} from "react-native-exception-handler";

import {
  crash,
  recordError,
  log,
  setUserId,
  setAttribute,
} from "@react-native-firebase/crashlytics";

// Import Firebase services from your config file
import { auth, db, crashlytics } from "./config/firebase";

// Add this to disable modular API deprecation warnings
// globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;

// Import screens and components
import { ToastContainer } from "./components/toast/ToastContainer";
import LoadingScreen from "./LoadingScreen";
import { red } from "./screens/colors";
import FastImage from "react-native-fast-image";

// Import language defaults
import { EN, CZ } from "./assets/strings";

// Screens
import Login from "./screens/Login";
import Signup from "./screens/Signup";
import SetupProfileNavigator from "./screens/SetupProfileNavigator";
import Home from "./screens/Home";
import MyGames from "./screens/MyGames";
import Map from "./screens/Map";
import Profile from "./screens/Profile";
import Game from "./screens/Game";
import MyGamesStack from "./screens/MyGamesStack";
import EditEvent from "./screens/EditEvent";
import EditCoords from "./screens/EditCoords";
import EditCoordsUpdated from "./screens/EditCoordsUpdated";
import CourtInfo from "./screens/CourtInfo";
import AddCourt from "./screens/AddCourt";
import Socials from "./screens/Socials";
import Teams from "./screens/Teams";
import NotificationsSc from "./screens/Notifications";
import DevTools from "./screens/DevTools";
import ChatsList from "./screens/ChatsList";
import ChatScreen from "./screens/ChatScreen";
import League from "./screens/League";
import User from "./screens/User";
import ChatMenu from "./screens/ChatMenu";
import ChatMedia from "./screens/ChatMedia";
import CreateTeam from "./screens/CreateTeam";
import ChooseTeamType from "./screens/ChooseTeamType";
import ChooseTeamLocation from "./screens/ChooseTeamLocation";
import SearchLocation from "./screens/SearchLocation";
import PinpointLocation from "./screens/PinPointLocation";
import CreateTrainingSession from "./screens/CreateTrainingSession";
import TeamDetails from "./screens/Teams/TeamDetails";
import TrainingSessionDetails from "./screens/Teams/TrainingSessionDetails";
import TrainingSessionHistory from "./screens/Teams/TrainingSessionHistory";
import SessionsSchedule from "./screens/Teams/SessionsSchedule";
import LocationsList from "./screens/Teams/LocationsList";
import TeamInbox from "./screens/Teams/TeamInbox";
import CourtsLeaderboardScreen from "./screens/CourtsLeaderboardScreen";

import ErrorBoundary from "react-native-error-boundary";

import ErrorScreen from "./components/ErrorScreen";
import VersionCheck from "./components/common/VersionCheck";
// impo

// Import redux actions from separate slice files
import {
  startSocialsListener,
  startUserDataListener,
  startFriendsListener,
} from "./slices/socialsSlice";
import { fetchLanguagePreference } from "./slices/languageSlice";
import { startGamesListener } from "./slices/gamesSlice";
import { startTeamsListener } from "./slices/teamsSlice";
import { startAppControlListener } from "./slices/appControlSlice";

import { recordCrashlyticsError } from "./functions/recordCrashlyticsError";

const preloadAppImages = () => {
  FastImage.preload([
    { uri: Image.resolveAssetSource(require("./images/error-hoop9.png")).uri },
  ]);
};

// Keep splash screen visible initially
SplashScreen.preventAutoHideAsync();

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Global error state
const globalErrorState = {
  hasError: false,
  error: null,
  setError: null,
  currentLanguage: null, // Store language here
};

// Custom error handlers
const handleJSError = (error, isFatal) => {
  // Log to Crashlytics
  recordCrashlyticsError(error, "Global JS Error Handler");
  if (isFatal) {
    log(crashlytics, "Fatal JS error occurred");
  }

  if (isFatal) {
    globalErrorState.hasError = true;
    globalErrorState.error = error;
    if (globalErrorState.setError) {
      globalErrorState.setError(error);
    }
  }
};

// 3. Extend the native error handler
const handleNativeError = (errorString) => {
  const nativeError = new Error(errorString);
  recordCrashlyticsError(nativeError, "Global Native Error Handler");
  log(crashlytics, "Native Error captured");

  globalErrorState.hasError = true;
  globalErrorState.error = new Error(errorString);
  if (globalErrorState.setError) {
    globalErrorState.setError(globalErrorState.error);
  }
};

// Register global error handlers
setJSExceptionHandler(handleJSError, true);
setNativeExceptionHandler(handleNativeError);

// Main Tab Navigator as a component
function TabNavigator() {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarInactiveTintColor: "black",
        tabBarActiveTintColor: red,
        tabBarStyle: {
          height: 59 + (insets?.bottom || 0) * 0.8,
          paddingTop: 10,
          backgroundColor: "white",
          borderTopWidth: 0,
        },
      }}
      initialRouteName="Teams"
    >
      {/* <Tab.Screen
        name="League"
        component={League}
        options={{
          lazy: false,
          tabBarIcon: ({ focused }) => (
            <AntDesign
              name="Trophy"
              size={32}
              color={focused ? red : "black"}
            />
          ),
        }}
      />

      <Tab.Screen
        name="My Games"
        component={MyGames}
        options={{
          lazy: false,
          tabBarIcon: ({ focused }) => (
            <Ionicons
              name="basketball-outline"
              size={32}
              color={focused ? red : "black"}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Teams"
        component={Teams}
        options={{
          tabBarIcon: ({ focused }) => (
            <MaterialCommunityIcons
              name="google-circles-communities"
              size={34}
              color={focused ? red : "black"}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Socials"
        component={Socials}
        options={{
          lazy: false,
          tabBarIcon: ({ focused }) => (
            <MaterialCommunityIcons
              name="account-group-outline"
              size={36}
              color={focused ? red : "black"}
            />
          ),
        }}
      /> */}

      <Tab.Screen
        name="Map"
        component={Map}
        options={{
          lazy: false,
          tabBarIcon: ({ focused }) => (
            <Feather name="map" size={30} color={focused ? red : "black"} />
          ),
        }}
      />

      <Tab.Screen
        name="DevTools"
        component={DevTools}
        options={{
          lazy: false,
          tabBarIcon: ({ focused }) => (
            <MaterialCommunityIcons
              name="tools"
              size={30}
              color={focused ? red : "black"}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

// Main App component with unified navigation
function AppNavigator() {
  const [appState, setAppState] = useState({
    status: "initializing", // 'initializing', 'authenticated', 'unauthenticated', 'profile_setup'
    user: null,
    profileExists: false,
  });

  const navigationRef = React.useRef(null);
  const dispatch = useDispatch();
  const isLoadingUserData = useSelector(
    (state) => state.socials.isLoadingUserData
  );
  const isLoadingChats = useSelector((state) => state.socials.isLoadingChats);
  const { language } = useSelector((state) => state.language) || {
    language: EN,
  };

  // Store current language in global state for error handling
  if (language) {
    globalErrorState.currentLanguage = language;
  }

  // Load fonts
  const [fontsLoaded] = useFonts({
    "BungeeInline-Regular": require("./assets/fonts/Bungee_Inline/BungeeInline-Regular.ttf"),
    "Nunito-Bold": require("./assets/fonts/Nunito/static/Nunito-Bold.ttf"),
    Anton: require("./assets/fonts/Anton/Anton-Regular.ttf"),
  });

  // Preload critical assets immediately after font loading
  useEffect(() => {
    if (fontsLoaded) {
      preloadAppImages();
    }
  }, [fontsLoaded]);

  // Profile check function
  const checkUserProfile = useCallback(async (user) => {
    if (!user) return false;

    try {
      const docSnapshot = await getDoc(doc(db, "users", user.uid));
      return docSnapshot.exists;
    } catch (error) {
      console.error("Error checking user profile:", error);
      return false;
    }
  }, []);

  // Setup redux listeners
  const setupReduxListeners = useCallback(
    (user) => {
      if (!user) return;

      dispatch(startSocialsListener(auth));
      dispatch(startUserDataListener(auth));
      dispatch(startFriendsListener(auth));
      dispatch(fetchLanguagePreference(auth));
      dispatch(startGamesListener(auth));
      dispatch(startTeamsListener(auth));
      dispatch(startAppControlListener());
    },
    [dispatch]
  );

  // Handle navigation based on auth status changes
  useEffect(() => {
    // Skip during initialization
    if (appState.status === "initializing") return;

    // Get navigation object
    const navigation = navigationRef.current;
    if (!navigation) return;

    // Handle navigation based on auth state
    if (appState.status === "unauthenticated") {
      // console.log("Navigating to Login screen due to unauthenticated status");
      navigation.reset({
        index: 0,
        routes: [{ name: "Login" }],
      });
    } else if (appState.status === "profile_setup") {
      // console.log(
      //   "Navigating to SetupProfileNavigator screen due to profile_setup status"
      // );
      navigation.reset({
        index: 0,
        routes: [{ name: "SetupProfileNavigator" }],
      });
    } else if (appState.status === "authenticated") {
      // console.log("Navigating to MainTabs screen due to authenticated status");
      navigation.reset({
        index: 0,
        routes: [{ name: "MainTabs" }],
      });
    }
  }, [appState.status]);

  // Auth state listener
  useEffect(() => {
    // console.log("Setting up auth state listener");

    const subscriber = onAuthStateChanged(auth, async (user) => {
      // console.log(
      //   "Auth state changed:",
      //   user ? "User authenticated" : "No user"
      // );

      if (user) {
        // Set Crashlytics user ID
        await setUserId(crashlytics, user.uid);

        // Optionally set additional user attributes
        await setAttribute(crashlytics, "email", user.email);
        await setAttribute(crashlytics, "userType", "authenticated");

        // User is authenticated
        const hasProfile = await checkUserProfile(user);
        // console.log("User has profile:", hasProfile);

        if (hasProfile) {
          // console.log("User has a complete profile");
          // User has a complete profile
          setAppState({
            status: "authenticated",
            user,
            profileExists: true,
          });
          setupReduxListeners(user);
        } else {
          // console.log("User needs to setup profile");
          // User is authenticated but needs profile setup
          setAppState({
            status: "profile_setup",
            user,
            profileExists: false,
          });
        }
      } else {
        // Clear Crashlytics user ID when logged out
        await setUserId(crashlytics, "");
        await setAttribute(crashlytics, "userType", "anonymous");

        // console.log("User is not authenticated");
        setAppState({
          status: "unauthenticated",
          user: null,
          profileExists: false,
        });
      }

      // Hide splash screen
      SplashScreen.hideAsync();
    });

    return subscriber; // Unsubscribe on unmount
  }, [checkUserProfile, setupReduxListeners]);

  if (appState.status === "initializing" || !fontsLoaded) {
    return <LoadingScreen message="Initializing..." />;
  }

  if (
    appState.status === "authenticated" &&
    (isLoadingUserData || isLoadingChats)
  ) {
    return <LoadingScreen message="Getting your data ready..." />;
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
        initialRouteName={
          appState.status === "unauthenticated"
            ? "Login"
            : appState.status === "profile_setup"
            ? "SetupProfileNavigator"
            : "MainTabs"
        }
      >
        {/* Authentication Screens */}
        <Stack.Screen name="Login" component={Login} />
        <Stack.Screen name="Signup" component={Signup} />

        {/* Profile Setup Screens - Now using the Navigator */}
        <Stack.Screen
          name="SetupProfileNavigator"
          component={SetupProfileNavigator}
          options={{ gestureEnabled: false }}
        />

        {/* Main App Screens */}
        <Stack.Screen name="MainTabs" component={TabNavigator} />
        <Stack.Screen name="Game" component={Game} />
        <Stack.Screen name="NotificationsSc" component={NotificationsSc} />
        <Stack.Screen name="ChatsList" component={ChatsList} />
        <Stack.Screen name="ChatScreen" component={ChatScreen} />
        <Stack.Screen name="CourtInfo" component={CourtInfo} />
        <Stack.Screen name="MyGamesStack" component={MyGamesStack} />
        <Stack.Screen name="EditEvent" component={EditEvent} />
        <Stack.Screen name="AddCourt" component={AddCourt} />
        <Stack.Screen name="EditCoordsUpdated" component={EditCoordsUpdated} />
        <Stack.Screen name="Profile" component={Profile} />
        <Stack.Screen name="User" component={User} />
        <Stack.Screen name="ChatMenu" component={ChatMenu} />
        <Stack.Screen name="ChatMedia" component={ChatMedia} />
        <Stack.Screen name="CreateTeam" component={CreateTeam} />
        <Stack.Screen name="ChooseTeamType" component={ChooseTeamType} />
        <Stack.Screen
          name="ChooseTeamLocation"
          component={ChooseTeamLocation}
        />
        <Stack.Screen name="SearchLocation" component={SearchLocation} />
        <Stack.Screen name="PinpointLocation" component={PinpointLocation} />
        <Stack.Screen name="TeamDetails" component={TeamDetails} />
        <Stack.Screen
          name="CreateTrainingSession"
          component={CreateTrainingSession}
        />
        <Stack.Screen
          name="TrainingSessionDetails"
          component={TrainingSessionDetails}
        />
        <Stack.Screen
          name="TrainingSessionHistory"
          component={TrainingSessionHistory}
        />
        <Stack.Screen name="SessionsSchedule" component={SessionsSchedule} />
        <Stack.Screen name="LocationsList" component={LocationsList} />
        <Stack.Screen name="TeamInbox" component={TeamInbox} />
        <Stack.Screen
          name="CourtsLeaderboard"
          component={CourtsLeaderboardScreen}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Main App component with error handling
export default function App() {
  const [error, setError] = useState(null);
  const [appKey, setAppKey] = useState(Date.now());

  // Store error setter in global error state
  globalErrorState.setError = setError;

  // Preload error screen assets on initial app load
  useEffect(() => {
    preloadAppImages();
  }, []);

  // Reset error and restart app
  const resetError = () => {
    globalErrorState.hasError = false;
    globalErrorState.error = null;
    setError(null);
    setAppKey(Date.now()); // Force complete re-render
  };

  // If there's an error, show the error screen with the last known language context
  if (globalErrorState.hasError || error) {
    return (
      <SafeAreaProvider>
        <ToastContainer>
          <ErrorScreen
            error={error || globalErrorState.error}
            resetError={resetError}
            language={globalErrorState.currentLanguage || EN}
          />
        </ToastContainer>
      </SafeAreaProvider>
    );
  }

  // Normal app flow with Redux
  return (
    <SafeAreaProvider key={appKey}>
      <ToastContainer>
        <Provider store={store}>
          <VersionCheck>
            <AppNavigator />
          </VersionCheck>
        </Provider>
      </ToastContainer>
    </SafeAreaProvider>
  );
}
